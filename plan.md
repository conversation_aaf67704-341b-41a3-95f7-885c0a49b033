
# Plan for Implementing "Exercise 5: Creating Single Page Applications with React.js"

This plan outlines the steps to complete the exercises in the provided PDF document. Each step corresponds to a section in the tutorial.

## 1. Initial Setup

- [x] Verify that the `tuiter-react-web-app` project is set up as described in the "Introduction" section.
- [x] Ensure that the `public/index.html` file contains a `div` with the ID `root`.
- [x] Verify that `src/index.js` is the entry point of the application.

## 2. "Hello World" and Basic Components

- [x] Replace the content of `src/App.js` with the "Hello World" example.
- [x] Start the application using `npm start` and confirm that "Hello World!" is displayed.
- [x] Install Bootstrap and Bootstrap Icons using `npm install bootstrap bootstrap-icons`.
- [x] Import the Bootstrap CSS files in `src/index.js`.

## 3. Implementing the Exercises Component

- [x] Create a new directory `src/Exercises`.
- [x] Create a file `src/Exercises/index.js` and implement the `Exercises` component.
- [x] Import and render the `Exercises` component in `src/App.js`.

## 4. Breaking Out Exercises into Separate Components

- [x] Create a new directory `src/Exercises/e5`.
- [x] Create a file `src/Exercises/e5/index.js` and implement the `Exercise5` component.
- [x] Import and render the `Exercise5` component in `src/Exercises/index.js`.
- [x] Create a `HelloWorld` component in `src/Exercises/e5/hello-world.js`.
- [x] Import and render the `HelloWorld` component in `src/App.js`.

## 5. Creating a Tuiter Placeholder Component

- [x] Create a new directory `src/tuiter`.
- [x] Create a file `src/tuiter/index.js` and implement the `Tuiter` placeholder component.
- [x] Import and render the `Tuiter` component in `src/App.js`.

## 6. Implementing Navigation

- [x] Install `react-router-dom` using `npm install react-router-dom`.
- [x] Set up `BrowserRouter` in `src/App.js`.
- [x] Define routes for `HelloWorld`, `Exercises`, and `Tuiter` components in `src/App.js`.
- [x] Implement navigation links in each of the `Exercise5`, `HelloWorld`, and `Tuiter` components.
- [x] Create a reusable `Nav` component in `src/nav.js`.
- [x] Import and use the `Nav` component in `HelloWorld`, `Exercises`, and `Tuiter`.

## 7. Working with HTML Classes and Styles

- [x] Create a `classes` directory inside `src/Exercises/e5`.
- [x] Implement the `Classes` component and its corresponding CSS.
- [x] Import and render the `Classes` component in `e5/index.js`.
- [x] Implement dynamic classes based on component state.
- [x] Create a `styles` directory inside `src/Exercises/e5`.
- [x] Implement the `Styles` component to demonstrate inline styling.
- [x] Import and render the `Styles` component in `e5/index.js`.

## 8. Generating Conditional Output

- [x] Create a `conditional-output` directory inside `src/Exercises/e5`.
- [x] Implement the `ConditionalOutputIfElse` and `ConditionalOutputInline` components.
- [x] Create a `ConditionalOutput` component that combines the two.
- [x] Import and render the `ConditionalOutput` component in `e5/index.js`.

## 9. Implementing the ToDo List

- [x] Create a `todo` directory inside `src/Exercises/e5`.
- [x] Create a `todos.json` file with sample to-do items.
- [x] Implement the `TodoItem` component.
- [x] Implement the `TodoList` component that fetches data from `todos.json` and renders a list of `TodoItem` components.
- [x] Import and render the `TodoList` component in `e5/index.js`.
