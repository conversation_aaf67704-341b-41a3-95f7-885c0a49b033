import Exercises from "./Exercises";
import HelloWorld from "./Exercises/e5/hello-world.js";
import Tuiter from "./tuiter";
import {BrowserRouter, Routes, Route} from "react-router-dom";
import Nav from "./nav";
function App() {
 return (
  <BrowserRouter>
   <div className="container-fluid">
    <div className="row">
     <div className="col-2">
      <Nav/>
     </div>
     <div className="col-10">
      <Routes>
       <Route index element={<Exercises/>}/>
       <Route path="/hello" element={<HelloWorld/>}/>
       <Route path="/tuiter" element={<Tuiter/>}/>
      </Routes>
     </div>
    </div>
   </div>
  </BrowserRouter>
 );
}
export default App;