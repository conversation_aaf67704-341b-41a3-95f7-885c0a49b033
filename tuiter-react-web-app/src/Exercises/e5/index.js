import Classes from "./classes";
import Styles from "./styles";
import ConditionalOutput from "./conditional-output";
import TodoList from "./todo/todo-list";
function Exercise5() {
 return(
  <div>
   <h1>Exercise 5</h1>
   <div class="card mb-3">
    <div class="card-header">
     Conditional Output
    </div>
    <div class="card-body">
     <ConditionalOutput/>
    </div>
   </div>
   <div class="card mb-3">
    <div class="card-header">
     Styles
    </div>
    <div class="card-body">
     <Styles/>
    </div>
   </div>
   <div class="card mb-3">
    <div class="card-header">
     Classes
    </div>
    <div class="card-body">
     <Classes/>
    </div>
   </div>
   <div class="card mb-3">
    <div class="card-header">
     Todo List
    </div>
    <div class="card-body">
     <TodoList/>
    </div>
   </div>
  </div>
 );
}
export default Exercise5;