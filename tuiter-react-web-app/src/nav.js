import {Link} from "react-router-dom";
function Nav() {
 return (
  <nav className="navbar navbar-expand-lg navbar-light bg-light">
   <div className="container-fluid">
    <Link className="navbar-brand" to="/">Tuiter App</Link>
    <div className="collapse navbar-collapse">
     <ul className="navbar-nav me-auto mb-2 mb-lg-0">
      <li className="nav-item">
       <Link className="nav-link" to="/">Exercise</Link>
      </li>
      <li className="nav-item">
       <Link className="nav-link" to="/hello">Hello</Link>
      </li>
      <li className="nav-item">
       <Link className="nav-link" to="/tuiter">Tuiter</Link>
      </li>
     </ul>
    </div>
   </div>
  </nav>
 )
}
export default Nav;